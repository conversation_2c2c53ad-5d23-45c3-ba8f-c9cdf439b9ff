package services

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type ICycleService interface {
	Find(id string) (*models.Cycle, core.IError)
	Pagination(filters *CycleFilters, pageOptions *core.PageOptions) (*repository.Pagination[models.Cycle], core.IError)
	FindCurrent() (*models.Cycle, core.IError)
	UsagesByDay(id string, projectID string) (*DailyUsageResponse, core.IError)
}

type cycleService struct {
	ctx core.IContext
}

type CycleFilters struct {
	Status    []string
	StartDate string
	EndDate   string
}

func (s cycleService) Find(id string) (*models.Cycle, core.IError) {
	return repo.Cycle(s.ctx).FindOne("id = ?", id)
}

func (s cycleService) Pagination(filters *CycleFilters, pageOptions *core.PageOptions) (*repository.Pagination[models.Cycle], core.IError) {
	return repo.Cycle(
		s.ctx,
		repo.CycleOrderBy(pageOptions),
		repo.CycleByStatus(filters.Status),
		repo.CycleByDateRange(filters.StartDate, filters.EndDate),
		repo.CycleBySearch(pageOptions.Q)).
		Pagination(pageOptions)
}

func (s cycleService) FindCurrent() (*models.Cycle, core.IError) {
	return repo.Cycle(s.ctx, repo.CycleCurrent()).FindOne()
}

func (s cycleService) UsagesByDay(id string, projectID string) (*DailyUsageResponse, core.IError) {
	// First verify that the cycle exists
	cycle, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// If projectID is provided, verify that the project exists
	if projectID != "" {
		projectService := NewProjectService(s.ctx)
		_, ierr := projectService.Find(projectID)
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}
	}

	// Query to get daily aggregated usage data
	type DailyUsageResult struct {
		Date                string  `json:"date"`
		TotalAmount         float64 `json:"total_amount"`
		TotalOfficialAmount float64 `json:"total_official_amount"`
		ProjectCount        int64   `json:"project_count"`
		HourCount           int64   `json:"hour_count"`
	}

	var dailyResults []DailyUsageResult
	var err error

	// Build query with optional project filtering
	if projectID != "" {
		err = s.ctx.DB().Raw(`
		SELECT
    date,
    amount as total_amount,
    official_amount as total_official_amount,
    1 as project_count
FROM (
    SELECT
        DATE(timestamp) as date,
        amount,
        official_amount,
        ROW_NUMBER() OVER (
            PARTITION BY DATE(timestamp) 
            ORDER BY timestamp DESC
        ) as rn
    FROM project_usages
    WHERE cycle_id = ? 
        AND project_id = ? 
        AND timestamp IS NOT NULL
) ranked
WHERE rn = 1
ORDER BY date ASC;
		`, id, projectID).Scan(&dailyResults).Error
	} else {
		err = s.ctx.DB().Raw(`
			SELECT
    date,
    SUM(amount) as total_amount,
    SUM(official_amount) as total_official_amount,
    COUNT(DISTINCT project_id) as project_count
FROM (
    SELECT
        DATE(timestamp) as date,
        amount,
        official_amount,
        project_id,
        ROW_NUMBER() OVER (
            PARTITION BY DATE(timestamp), project_id
            ORDER BY timestamp DESC
        ) as rn
    FROM project_usages
    WHERE cycle_id = ? 
        AND timestamp IS NOT NULL
) ranked
WHERE rn = 1
GROUP BY date
ORDER BY date ASC
		`, id).Scan(&dailyResults).Error
	}

	if err != nil {
		return nil, s.ctx.NewError(err, core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "GET_DAILY_USAGE_FAILED",
			Message: "Failed to get daily usage data",
		})
	}

	// Convert results to response format
	dailyUsage := make([]DailyUsageData, len(dailyResults))
	var totalAmount, totalOfficialAmount float64
	var totalProjectCount, totalHourCount int

	for i, result := range dailyResults {
		dailyUsage[i] = DailyUsageData{
			Date:                result.Date,
			TotalAmount:         result.TotalAmount,
			TotalOfficialAmount: result.TotalOfficialAmount,
			ProjectCount:        int(result.ProjectCount),
		}

		totalAmount += result.TotalAmount
		totalOfficialAmount += result.TotalOfficialAmount
		if int(result.ProjectCount) > totalProjectCount {
			totalProjectCount = int(result.ProjectCount)
		}
		totalHourCount += int(result.HourCount)
	}

	// Calculate average daily amount
	averageDaily := 0.0
	if len(dailyResults) > 0 {
		averageDaily = totalAmount / float64(len(dailyResults))
	}

	// Build response
	var projectName *string
	var filteredProjectID *string

	if projectID != "" {
		// Get project details for filtered response
		projectService := NewProjectService(s.ctx)
		project, ierr := projectService.Find(projectID)
		if ierr == nil {
			projectName = &project.Name
			filteredProjectID = &project.ID
		}
	}

	response := &DailyUsageResponse{
		CycleID:     cycle.ID,
		StartDate:   cycle.CycleStartDate,
		EndDate:     cycle.CycleEndDate,
		ProjectID:   filteredProjectID,
		ProjectName: projectName,
		DailyUsage:  dailyUsage,
		Summary: UsageSummary{
			TotalAmount:         totalAmount,
			TotalOfficialAmount: totalOfficialAmount,
			TotalProjectCount:   totalProjectCount,
			TotalHourCount:      totalHourCount,
			DaysWithData:        len(dailyResults),
			AverageDaily:        averageDaily,
		},
	}

	return response, nil
}

func NewCycleService(ctx core.IContext) ICycleService {
	return &cycleService{ctx: ctx}
}
