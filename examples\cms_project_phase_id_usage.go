package examples

import (
	"fmt"

	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

// Example functions showing how to work with phase_id from CMS projects

// GetCurrentPhaseIDFromProject demonstrates how to get the current phase_id from a project
func GetCurrentPhaseIDFromProject(ctx core.IContext, projectID string) (*string, error) {
	cmsProjectSvc := services.NewCMSProjectService(ctx)

	// Get the project
	project, err := cmsProjectSvc.Find(projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to find project: %v", err)
	}

	// Return the current phase_id (could be nil)
	return project.PhaseID, nil
}

// FindProjectsWithSamePhase demonstrates how to find all projects with the same current phase
func FindProjectsWithSamePhase(ctx core.IContext, referenceProjectID string) ([]*models.CMSProject, error) {
	cmsProjectSvc := services.NewCMSProjectService(ctx)

	// First, get the reference project's phase_id
	referenceProject, err := cmsProjectSvc.Find(referenceProjectID)
	if err != nil {
		return nil, fmt.Errorf("failed to find reference project: %v", err)
	}

	// If the reference project doesn't have a current phase, return empty
	if referenceProject.PhaseID == nil {
		return []*models.CMSProject{}, nil
	}

	// Note: CMSProjectFilters doesn't have PhaseID field directly
	// We need to use a different approach to find projects with the same phase
	// This is a limitation of the current filter structure

	// For now, we'll get all projects and filter them manually
	// In a production system, you might want to add PhaseID to CMSProjectFilters
	filters := &services.CMSProjectFilters{}

	// Get projects with pagination
	pageOptions := &core.PageOptions{
		Page:  1,
		Limit: 1000, // Get a large number to filter manually
	}

	result, err := cmsProjectSvc.Pagination(filters, pageOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to get projects: %v", err)
	}

	// Filter projects manually to find those with the same phase_id
	var matchingProjects []*models.CMSProject
	for i := range result.Items {
		project := &result.Items[i]
		if project.PhaseID != nil && *project.PhaseID == *referenceProject.PhaseID {
			matchingProjects = append(matchingProjects, project)
		}
	}

	return matchingProjects, nil
}

// CheckIfProjectHasCurrentPhase demonstrates how to check if a project has a current phase
func CheckIfProjectHasCurrentPhase(ctx core.IContext, projectID string) (bool, *models.CMSProjectPhase, error) {
	cmsProjectSvc := services.NewCMSProjectService(ctx)

	// Get the project with relations (including Phase)
	project, err := cmsProjectSvc.Find(projectID)
	if err != nil {
		return false, nil, fmt.Errorf("failed to find project: %v", err)
	}

	// Check if project has a current phase
	if project.PhaseID == nil {
		return false, nil, nil
	}

	// If Phase relation is loaded, return it
	if project.Phase != nil {
		return true, project.Phase, nil
	}

	// If Phase relation is not loaded, we know it exists but don't have details
	return true, nil, nil
}

// FilterProjectsByPhaseAndStatus demonstrates combining phase_id filter with other filters
func FilterProjectsByPhaseAndStatus(ctx core.IContext, phaseID string, status []string) ([]*models.CMSProject, error) {
	cmsProjectSvc := services.NewCMSProjectService(ctx)

	// Create filters with status (PhaseID is not available in CMSProjectFilters)
	filters := &services.CMSProjectFilters{
		Status: status,
	}

	pageOptions := &core.PageOptions{
		Page:  1,
		Limit: 1000, // Get more items to filter manually
	}

	result, err := cmsProjectSvc.Pagination(filters, pageOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to filter projects: %v", err)
	}

	// Filter by phase_id manually
	var matchingProjects []*models.CMSProject
	for i := range result.Items {
		project := &result.Items[i]
		if project.PhaseID != nil && *project.PhaseID == phaseID {
			matchingProjects = append(matchingProjects, project)
		}
	}

	return matchingProjects, nil
}

// GetProjectCurrentPhaseDetails demonstrates how to get full current phase details
func GetProjectCurrentPhaseDetails(ctx core.IContext, projectID string) (*models.CMSProjectPhase, error) {
	cmsProjectSvc := services.NewCMSProjectService(ctx)
	cmsProjectPhaseSvc := services.NewCMSProjectPhaseService(ctx)

	// Get the project
	project, err := cmsProjectSvc.Find(projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to find project: %v", err)
	}

	// Check if project has a current phase
	if project.PhaseID == nil {
		return nil, fmt.Errorf("project does not have a current phase")
	}

	// Get the full phase details
	phase, err := cmsProjectPhaseSvc.Find(*project.PhaseID)
	if err != nil {
		return nil, fmt.Errorf("failed to find current phase: %v", err)
	}

	return phase, nil
}

// Example usage in a controller or service method:
/*
func ExampleControllerMethod(c core.IHTTPContext) error {
	projectID := c.Param("id")

	// Get current phase_id
	phaseID, err := GetCurrentPhaseIDFromProject(c, projectID)
	if err != nil {
		return c.JSON(500, map[string]string{"error": err.Error()})
	}

	if phaseID == nil {
		return c.JSON(200, map[string]interface{}{
			"project_id": projectID,
			"phase_id":   nil,
			"message":    "Project does not have a current phase",
		})
	}

	// Find other projects with the same phase
	similarProjects, err := FindProjectsWithSamePhase(c, projectID)
	if err != nil {
		return c.JSON(500, map[string]string{"error": err.Error()})
	}

	return c.JSON(200, map[string]interface{}{
		"project_id":       projectID,
		"current_phase_id": *phaseID,
		"similar_projects": similarProjects,
	})
}
*/
