### Cycle Usages By Day API Test File
### This file demonstrates how to get daily usage data for cycles with optional project filtering

@baseUrl = http://localhost:3001
@authToken = your_auth_token_here
@cycleId = your_cycle_id_here
@projectId = your_project_id_here

### 1. Get daily usage data for a cycle (all projects)
GET {{baseUrl}}/cycles/{{cycleId}}/usages
Authorization: Bearer {{authToken}}

### 2. Get daily usage data for a cycle filtered by specific project
GET {{baseUrl}}/cycles/{{cycleId}}/usages?project_id={{projectId}}
Authorization: Bearer {{authToken}}

### 3. Get current cycle first, then get its daily usage data
GET {{baseUrl}}/cycles/current
Authorization: Bearer {{authToken}}

### 4. Get all cycles to find a specific cycle ID
GET {{baseUrl}}/cycles
Authorization: Bearer {{authToken}}

### 5. Get all projects to find a specific project ID for filtering
GET {{baseUrl}}/projects
Authorization: Bearer {{authToken}}

### Expected Response Format (without project filter):
# {
#   "cycle_id": "uuid",
#   "cycle_name": "Cycle 1",
#   "start_date": "2024-01-01T00:00:00Z",
#   "end_date": "2024-01-31T23:59:59Z",
#   "daily_usage": [
#     {
#       "date": "2024-01-01",
#       "total_amount": 100.50,
#       "total_official_amount": 95.25,
#       "project_count": 5,
#       "hour_count": 24
#     },
#     {
#       "date": "2024-01-02",
#       "total_amount": 120.75,
#       "total_official_amount": 115.50,
#       "project_count": 6,
#       "hour_count": 48
#     }
#   ],
#   "summary": {
#     "total_amount": 3150.75,
#     "total_official_amount": 2993.25,
#     "total_project_count": 15,
#     "total_hour_count": 744,
#     "days_with_data": 31,
#     "average_daily_amount": 101.64
#   }
# }

### Expected Response Format (with project filter):
# {
#   "cycle_id": "uuid",
#   "cycle_name": "Cycle 1 - Project Name",
#   "start_date": "2024-01-01T00:00:00Z",
#   "end_date": "2024-01-31T23:59:59Z",
#   "project_id": "project-uuid",
#   "project_name": "Project Name",
#   "daily_usage": [
#     {
#       "date": "2024-01-01",
#       "total_amount": 25.50,
#       "total_official_amount": 24.25,
#       "project_count": 1,
#       "hour_count": 24
#     }
#   ],
#   "summary": {
#     "total_amount": 750.75,
#     "total_official_amount": 713.25,
#     "total_project_count": 1,
#     "total_hour_count": 744,
#     "days_with_data": 31,
#     "average_daily_amount": 24.22
#   }
# }
