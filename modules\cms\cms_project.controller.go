package cms

import (
	"net/http"
	"strings"

	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/requests"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type CMSProjectController struct {
}

func (m CMSProjectController) Dashboard(c core.IHTTPContext) error {
	cmsProjectSvc := services.NewCMSProjectService(c)
	dashboard, err := cmsProjectSvc.GetDashboardData()
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, dashboard)
}

func (m CMSProjectController) Pagination(c core.IHTTPContext) error {
	cmsProjectSvc := services.NewCMSProjectService(c)

	// Parse status parameter
	var statuses []string
	statusParam := c.QueryParam("status")
	if statusParam != "" {
		statusList := strings.Split(statusParam, ",")
		for _, status := range statusList {
			trimmedStatus := strings.TrimSpace(status)
			if trimmedStatus != "" {
				statuses = append(statuses, trimmedStatus)
			}
		}
	}

	// Parse type parameter
	var types []string
	typeParam := c.QueryParam("type")
	if typeParam != "" {
		typeList := strings.Split(typeParam, ",")
		for _, typ := range typeList {
			trimmedType := strings.TrimSpace(typ)
			if trimmedType != "" {
				types = append(types, trimmedType)
			}
		}
	}

	// Parse filters from query parameters
	filters := &services.CMSProjectFilters{
		Status:          statuses,
		Type:            types,
		MinistryID:      c.QueryParam("ministry_id"),
		DepartmentID:    c.QueryParam("department_id"),
		DivisionID:      c.QueryParam("division_id"),
		StartDate:       c.QueryParam("start_date"),
		EndDate:         c.QueryParam("end_date"),
		Phase:           c.QueryParam("phase"),
		WorkPhase:       c.QueryParam("work_phase"),
		IsNearlyExpired: c.QueryParam("is_nearly_expired") == "true",
	}

	res, ierr := cmsProjectSvc.Pagination(filters, c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m CMSProjectController) Find(c core.IHTTPContext) error {
	cmsProjectSvc := services.NewCMSProjectService(c)
	cmsProject, err := cmsProjectSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, cmsProject)
}

func (m CMSProjectController) Create(c core.IHTTPContext) error {
	input := &requests.CMSProjectCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	cmsProjectSvc := services.NewCMSProjectService(c)
	payload := &services.CMSProjectCreatePayload{}
	_ = utils.Copy(payload, input)

	cmsProject, err := cmsProjectSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, cmsProject)
}

func (m CMSProjectController) Update(c core.IHTTPContext) error {
	input := &requests.CMSProjectUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	cmsProjectSvc := services.NewCMSProjectService(c)
	payload := &services.CMSProjectUpdatePayload{}
	_ = utils.Copy(payload, input)

	cmsProject, err := cmsProjectSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, cmsProject)
}

func (m CMSProjectController) Delete(c core.IHTTPContext) error {
	cmsProjectSvc := services.NewCMSProjectService(c)
	err := cmsProjectSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}

func (m CMSProjectController) UpdateStatus(c core.IHTTPContext) error {
	input := &requests.CMSProjectStatusUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	cmsProjectSvc := services.NewCMSProjectService(c)
	cmsProject, err := cmsProjectSvc.UpdateStatus(c.Param("id"), models.CMSProjectStatus(utils.ToNonPointer(input.Status)))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, cmsProject)
}
