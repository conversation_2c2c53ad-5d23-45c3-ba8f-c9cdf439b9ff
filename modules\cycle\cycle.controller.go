package cycle

import (
	"net/http"
	"strings"

	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type CycleController struct {
}

func (m CycleController) Pagination(c core.IHTTPContext) error {
	// Parse status parameter
	var statuses []string
	statusParam := c.QueryParam("status")
	if statusParam != "" {
		// Split comma-separated status values and trim whitespace
		statusList := strings.Split(statusParam, ",")
		for _, status := range statusList {
			trimmedStatus := strings.TrimSpace(status)
			if trimmedStatus != "" {
				statuses = append(statuses, trimmedStatus)
			}
		}
	}

	filters := &services.CycleFilters{
		StartDate: c.Query<PERSON>("start_date"),
		EndDate:   c.QueryParam("end_date"),
		Status:    statuses,
	}

	cycleSvc := services.NewCycleService(c)
	res, ierr := cycleSvc.Pagination(filters, c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m CycleController) Find(c core.IHTTPContext) error {
	cycleSvc := services.NewCycleService(c)
	cycle, err := cycleSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, cycle)
}

func (m CycleController) FindCurrent(c core.IHTTPContext) error {
	cycleSvc := services.NewCycleService(c)
	cycle, err := cycleSvc.FindCurrent()
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, cycle)
}

func (m CycleController) UsagesByDay(c core.IHTTPContext) error {
	cycleSvc := services.NewCycleService(c)

	// Get optional project_id query parameter for filtering
	projectID := c.QueryParam("project_id")

	usages, err := cycleSvc.UsagesByDay(c.Param("id"), projectID)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, usages)
}
