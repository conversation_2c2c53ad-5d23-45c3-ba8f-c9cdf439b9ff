package services

import "time"

// DailyUsageResponse represents the response structure for daily usage data
type DailyUsageResponse struct {
	CycleID     string           `json:"cycle_id"`
	StartDate   time.Time        `json:"start_date"`
	EndDate     time.Time        `json:"end_date"`
	ProjectID   *string          `json:"project_id,omitempty"`
	ProjectName *string          `json:"project_name,omitempty"`
	DailyUsage  []DailyUsageData `json:"daily_usage"`
	Summary     UsageSummary     `json:"summary"`
}

// DailyUsageData represents usage data for a specific day
type DailyUsageData struct {
	Date                string  `json:"date"`
	TotalAmount         float64 `json:"total_amount"`
	TotalOfficialAmount float64 `json:"total_official_amount"`
	ProjectCount        int     `json:"project_count"`
}

// UsageSummary provides aggregated statistics for the cycle
type UsageSummary struct {
	TotalAmount         float64 `json:"total_amount"`
	TotalOfficialAmount float64 `json:"total_official_amount"`
	TotalProjectCount   int     `json:"total_project_count"`
	TotalHourCount      int     `json:"total_hour_count"`
	DaysWithData        int     `json:"days_with_data"`
	AverageDaily        float64 `json:"average_daily_amount"`
}
