# CMS Dashboard API

This document describes the CMS Dashboard API endpoint that provides comprehensive statistics and insights about CMS projects.

## Overview

The CMS Dashboard provides a comprehensive overview of all CMS projects including:
- Project statistics by status
- Latest projects
- Nearly expired projects (within 3 months)
- Phase statistics with counts and nearly expired counts

## Endpoint

### Get Dashboard Data

**GET** `/cms/dashboard`

Returns comprehensive dashboard data for CMS projects.

#### Authentication
- **Required**: Yes
- **Type**: <PERSON><PERSON>
- **Middleware**: `AuthMiddleware()`

#### Request

```http
GET /cms/dashboard HTTP/1.1
Host: api.example.com
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

#### Response

**Success Response (200 OK)**

```json
{
  "project_stats": {
    "total": 150,
    "draft": 25,
    "new": 30,
    "in_process": 45,
    "active": 35,
    "close": 15,
    "nearly_expired": 8
  },
  "latest_projects": [
    {
      "id": "project-uuid-1",
      "ministry_id": "ministry-uuid",
      "department_id": "department-uuid",
      "division_id": "division-uuid",
      "phase_id": "phase-uuid",
      "name": "Government Portal System",
      "type": "NEW",
      "domain": "portal.gov.th",
      "contact_name": "<PERSON> Doe",
      "contact_phone": "+66-2-123-4567",
      "contact_email": "<EMAIL>",
      "remark": "High priority project",
      "status": "ACTIVE",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-20T14:45:00Z",
      "ministry": {
        "id": "ministry-uuid",
        "name": "Ministry of Digital Economy and Society",
        "code": "MDES"
      },
      "department": {
        "id": "department-uuid",
        "name": "Digital Government Development Agency",
        "code": "DGA"
      },
      "division": {
        "id": "division-uuid",
        "name": "System Development Division",
        "code": "SDD"
      },
      "phase": {
        "id": "phase-uuid",
        "cms_project_id": "project-uuid-1",
        "phase": 2,
        "work_phase": 1,
        "start_date": "2024-01-01",
        "end_date": "2024-06-30",
        "type": "DEVELOPMENT",
        "file_url": "https://storage.example.com/phase-docs/phase-2.pdf"
      }
    }
    // ... more projects (up to 10)
  ],
  "nearly_expired_projects": [
    {
      "id": "project-uuid-2",
      "name": "E-Government Services",
      "status": "ACTIVE",
      "phase": {
        "phase": 3,
        "end_date": "2024-04-15"
      }
      // ... full project details
    }
    // ... more nearly expired projects
  ],
  "phase_states": [
    {
      "phase": 1,
      "count": 45,
      "nearly_expired_count": 2
    },
    {
      "phase": 2,
      "count": 38,
      "nearly_expired_count": 3
    },
    {
      "phase": 3,
      "count": 25,
      "nearly_expired_count": 3
    }
  ]
}
```

#### Response Fields

##### Project Stats Object
- `total`: Total number of CMS projects
- `draft`: Number of projects in DRAFT status
- `new`: Number of projects in NEW status
- `in_process`: Number of projects in IN_PROCESS status
- `active`: Number of projects in ACTIVE status
- `close`: Number of projects in CLOSE status
- `nearly_expired`: Number of projects with phases ending within 3 months

##### Latest Projects Array
Contains up to 10 most recently created projects with full relations:
- `id`: Project UUID
- `name`: Project name
- `type`: Project type (NEW, MAINTENANCE, UPGRADE)
- `domain`: Project domain
- `status`: Project status
- `ministry`: Related ministry information
- `department`: Related department information
- `division`: Related division information
- `phase`: Current phase information (if exists)

##### Nearly Expired Projects Array
Contains projects with current phases ending within 3 months, with full project details and relations.

##### Phase Stats Array
- `phase`: Phase number
- `count`: Total count of projects currently in this phase
- `nearly_expired_count`: Count of projects in this phase that are nearly expired

#### Error Responses

**Unauthorized (401)**
```json
{
  "code": "UNAUTHORIZED",
  "message": "Authentication required"
}
```

**Internal Server Error (500)**
```json
{
  "code": "INTERNAL_ERROR",
  "message": "Failed to get dashboard data"
}
```

## Implementation Details

### Performance Optimization
- **Concurrent Processing**: Dashboard data is fetched using goroutines for optimal performance
- **Parallel Queries**: Project stats, latest projects, nearly expired projects, and phase stats are fetched simultaneously
- **Efficient Queries**: Uses optimized database queries with proper indexing

### Business Logic
- **Nearly Expired**: Projects with current phases ending within 3 months from the current date
- **Latest Projects**: 10 most recently created projects ordered by creation date
- **Phase Statistics**: Only counts projects that have active phases (phase_id is not null)
- **Full Relations**: All projects include related ministry, department, division, and phase information

### Data Consistency
- All statistics are calculated in real-time
- Phase statistics only include currently active phases
- Nearly expired calculation is based on the current phase's end date

## Usage Examples

### Basic Dashboard Request
```bash
curl -X GET "https://api.example.com/cms/dashboard" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

### JavaScript/Frontend Usage
```javascript
const response = await fetch('/cms/dashboard', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

const dashboardData = await response.json();
console.log('Total Projects:', dashboardData.project_stats.total);
console.log('Nearly Expired:', dashboardData.project_stats.nearly_expired);
```

## Related Endpoints

- `GET /cms/projects` - Get paginated list of CMS projects
- `GET /cms/projects/:id` - Get specific CMS project details
- `GET /cms/projects/:project_id/phases` - Get project phases

## Notes

- This endpoint requires authentication
- Response includes full project relations for better frontend integration
- Phase statistics are calculated based on currently active phases only
- Nearly expired threshold is fixed at 3 months and cannot be customized via API
