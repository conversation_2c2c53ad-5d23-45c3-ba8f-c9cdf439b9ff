package cycle

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/csp/csp-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewCycleHTTP(e *echo.Echo) {
	cycle := &CycleController{}

	// Cycle routes - all require authentication
	e.GET("/cycles", core.WithHTTPContext(cycle.Pagination), middleware.AuthMiddleware())
	e.GET("/cycles/current", core.WithHTTPContext(cycle.FindCurrent), middleware.AuthMiddleware())
	e.GET("/cycles/:id", core.WithHTTPContext(cycle.Find), middleware.AuthMiddleware())
	e.GET("/cycles/:id/usages", core.WithHTTPContext(cycle.UsagesByDay), middleware.AuthMiddleware())
}
