### CMS Project Phase ID Test File
### This file demonstrates how to get and use phase_id from CMS projects

@baseUrl = http://localhost:3001
@authToken = your_auth_token_here
@projectId = your_project_id_here

### 1. Get current phase information for a specific project
GET {{baseUrl}}/cms/projects/{{projectId}}/current-phase
Authorization: Bearer {{authToken}}

### 2. Get a specific project (includes phase_id in the response)
GET {{baseUrl}}/cms/projects/{{projectId}}
Authorization: Bearer {{authToken}}

### 3. Filter projects by phase_id (using the phase_id from previous responses)
GET {{baseUrl}}/cms/projects?phase_id=phase-id-from-previous-response
Authorization: Bearer {{authToken}}

### 4. Filter projects by phase_id with other filters
GET {{baseUrl}}/cms/projects?phase_id=phase-id-here&status=ACTIVE&ministry_id=ministry-id-here
Authorization: Bearer {{authToken}}

### 5. Get all projects and check their phase_id values
GET {{baseUrl}}/cms/projects?page=1&limit=10
Authorization: Bearer {{authToken}}

### 6. Example: Get projects with same phase as a specific project
### Step 1: First get the project to extract its phase_id
GET {{baseUrl}}/cms/projects/{{projectId}}/current-phase
Authorization: Bearer {{authToken}}

### Step 2: Use the phase_id from step 1 to find similar projects
### Replace "extracted-phase-id" with the actual phase_id from step 1
GET {{baseUrl}}/cms/projects?phase_id=extracted-phase-id
Authorization: Bearer {{authToken}}

### 7. Get project phases for a specific project
GET {{baseUrl}}/cms/projects/{{projectId}}/phases
Authorization: Bearer {{authToken}}

### 8. Create a new project phase (this will update the project's phase_id)
POST {{baseUrl}}/cms/projects/{{projectId}}/phases
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "phase": 2,
  "work_phase": 1,
  "start_date": "2024-01-01",
  "end_date": "2024-06-30",
  "type": "NEW",
  "file_url": "https://example.com/phase2.pdf"
}

### 9. After creating a new phase, check the updated current phase
GET {{baseUrl}}/cms/projects/{{projectId}}/current-phase
Authorization: Bearer {{authToken}}

### 10. Find all projects that don't have a current phase (phase_id is null)
### Note: This would require a separate endpoint or modification to handle null values
### For now, you can check individual projects to see if phase_id is null
