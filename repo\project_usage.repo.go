package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type ProjectUsageOption func(repository.IRepository[models.ProjectUsage])

var ProjectUsage = func(c core.IContext, options ...ProjectUsageOption) repository.IRepository[models.ProjectUsage] {
	r := repository.New[models.ProjectUsage](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func ProjectUsageOrderBy(pageOptions *core.PageOptions) ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("timestamp DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func ProjectUsageWithProject() ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		c.Preload("Project")
	}
}

func ProjectUsageWithCycle() ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		c.Preload("Cycle")
	}
}

func ProjectUsageWithAllRelations() ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		c.Preload("Project")
		c.Preload("Cycle")
	}
}

func ProjectUsageByProject(projectID string) ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		c.Where("project_id = ?", projectID)
	}
}

func ProjectUsageByCycle(cycleID string) ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		c.Where("cycle_id = ?", cycleID)
	}
}

func ProjectUsageByAmountRange(minAmount, maxAmount *float64) ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		if minAmount != nil && maxAmount != nil {
			c.Where("amount >= ? AND amount <= ?", *minAmount, *maxAmount)
		} else if minAmount != nil {
			c.Where("amount >= ?", *minAmount)
		} else if maxAmount != nil {
			c.Where("amount <= ?", *maxAmount)
		}
	}
}

func ProjectUsageBySearch(search string) ProjectUsageOption {
	if search == "" {
		return func(c repository.IRepository[models.ProjectUsage]) {}
	}
	return func(c repository.IRepository[models.ProjectUsage]) {
		// Search by ID or amount
		c.Where("id::text ILIKE ? OR amount::text ILIKE ?", "%"+search+"%", "%"+search+"%")
	}
}

func ProjectUsageByCurrentCycle() ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		c.Joins("JOIN cycles ON project_usages.cycle_id = cycles.id").
			Where("cycles.status = ?", "current")
	}
}

func ProjectUsageLatestPerCycleByProject(projectID string) ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		// Use raw SQL to get only the latest ProjectUsage for each cycle for a specific project
		c.Where("id IN (SELECT DISTINCT ON (cycle_id) id FROM project_usages WHERE project_id = ? ORDER BY cycle_id, timestamp DESC)", projectID)
	}
}

// ProjectUsageByDateRange filters project usage by timestamp date range
func ProjectUsageByDateRange(startDate, endDate string) ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		if startDate != "" && endDate != "" {
			c.Where("DATE(timestamp) >= ? AND DATE(timestamp) <= ?", startDate, endDate)
		} else if startDate != "" {
			c.Where("DATE(timestamp) >= ?", startDate)
		} else if endDate != "" {
			c.Where("DATE(timestamp) <= ?", endDate)
		}
	}
}

// ProjectUsageGroupByDay groups project usage by day for aggregation
func ProjectUsageGroupByDay() ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		c.Group("DATE(timestamp)")
	}
}
